#ifndef AMSSDK_API_RESULT_H
#define AMSSDK_API_RESULT_H

#include <string>
#include <optional>
#include <utility>
#include <nlohmann/json.hpp>
#include "network/response.h"

namespace amssdk {

/**
 * @brief Comprehensive error information for API calls
 */
struct SdkError {
  int status_code = 0;           ///< HTTP status code
  std::string code;              ///< API-specific error code
  std::string message;           ///< Human-readable error message
  std::string reason;            ///< HTTP reason phrase
  std::string details;           ///< Additional error details from API response

  /**
   * @brief Check if this represents an error state
   * @return true if there is an error, false otherwise
   */
  bool has_error() const noexcept {
    return status_code != 0 || !code.empty() || !message.empty();
  }

  /**
   * @brief Clear all error information
   */
  void clear() noexcept {
    status_code = 0;
    code.clear();
    message.clear();
    reason.clear();
    details.clear();
  }
};

/**
 * @brief Base class for API results to avoid code duplication
 */
class ApiResultBase {
public:
  bool ok = false;                    ///< HTTP-level success (2xx status codes)
  int status_code = 0;               ///< HTTP status code
  double elapsed_ms = 0.0;           ///< Request duration in milliseconds
  std::string request_url;           ///< The URL that was requested
  std::string raw_body;              ///< Raw response body as string
  SdkError error;                    ///< Error information if any

  /**
   * @brief Check if the request was successful at HTTP level
   * @return true if HTTP status indicates success (2xx)
   */
  bool is_http_success() const noexcept { return ok; }

  /**
   * @brief Check if there are any errors
   * @return true if there are no errors
   */
  bool is_success() const noexcept { return ok && !error.has_error(); }

  /**
   * @brief Get a human-readable error message
   * @return Error message or empty string if no error
   */
  std::string get_error_message() const {
    if (!error.message.empty()) return error.message;
    if (!error.reason.empty()) return error.reason;
    if (!error.details.empty()) return error.details;
    if (!ok) return "HTTP request failed with status " + std::to_string(status_code);
    return "";
  }

protected:
  /**
   * @brief Populate base fields from Response object
   * @param response The HTTP response to extract data from
   */
  void populate_from_response(const Response& response);
};

/**
 * @brief Template for API results with typed data
 * @tparam T The type of data returned by the API
 */
template <typename T>
struct ApiResult : public ApiResultBase {
  std::optional<T> data;             ///< Parsed response data (optional)

  /**
   * @brief Check if response data is available
   * @return true if data is present and valid
   */
  bool has_data() const noexcept { return data.has_value(); }

  /**
   * @brief Get the response data
   * @return Reference to the data
   * @throws std::bad_optional_access if data is not available
   */
  const T& get_data() const { return data.value(); }

  /**
   * @brief Get the response data with fallback
   * @param default_value Value to return if data is not available
   * @return The data or default value
   */
  const T& get_data_or(const T& default_value) const {
    return data.value_or(default_value);
  }

  /**
   * @brief Set the response data
   * @param value The data to set
   */
  void set_data(T&& value) { data = std::move(value); }
  void set_data(const T& value) { data = value; }
};

/**
 * @brief Specialization for void responses (no data expected)
 */
template <>
struct ApiResult<void> : public ApiResultBase {
  // No additional members needed for void specialization
};

/**
 * @brief Build an ApiResult from a Response object
 * @tparam T The expected data type
 * @param response The HTTP response to process
 * @return ApiResult with populated metadata and error information
 */
template <typename T>
ApiResult<T> BuildResult(const Response& response) {
  ApiResult<T> result;
  result.populate_from_response(response);
  return result;
}

/**
 * @brief Build an ApiResult and attempt to parse JSON data
 * @tparam T The expected data type
 * @param response The HTTP response to process
 * @param parser Function to parse JSON into T
 * @return ApiResult with populated data if parsing succeeds
 */
template <typename T, typename Parser>
ApiResult<T> BuildResultWithParser(const Response& response, Parser&& parser) {
  ApiResult<T> result;
  result.populate_from_response(response);

  if (result.is_success() && !response.raw_json.empty()) {
    try {
      result.set_data(parser(response.raw_json));
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.code = "PARSE_ERROR";
      result.error.message = "Failed to parse response data: " + std::string(e.what());
    }
  }

  return result;
}

}  // namespace amssdk

#endif  // AMSSDK_API_RESULT_H
