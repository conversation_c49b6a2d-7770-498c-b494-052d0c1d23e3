#include "common/api_result.h"
#include <stdexcept>

namespace amssdk {

void ApiResultBase::populate_from_response(const Response& response) {
  // Basic HTTP information
  status_code = response.status_code;
  elapsed_ms = response.elapsed;
  request_url = response.url;
  
  // Determine HTTP-level success
  ok = (status_code >= 200 && status_code < 300);
  
  // Handle raw body safely
  try {
    if (!response.raw_json.empty()) {
      raw_body = response.raw_json.dump();
    } else if (!response.content.empty()) {
      raw_body = response.content;
    }
  } catch (const std::exception& e) {
    // If JSON dumping fails, fall back to content
    raw_body = response.content;
  }
  
  // Clear any existing error state
  error.clear();
  
  // Handle HTTP-level errors
  if (!ok) {
    error.status_code = status_code;
    error.reason = response.reason;
    error.message = response.status_line;
    
    // Try to extract API error details from JSON response
    if (!response.raw_json.empty()) {
      try {
        const auto& json = response.raw_json;
        
        // Common API error patterns
        if (json.contains("error")) {
          const auto& error_obj = json["error"];
          
          if (error_obj.is_object()) {
            // Structured error object
            if (error_obj.contains("code")) {
              error.code = error_obj["code"].get<std::string>();
            }
            if (error_obj.contains("message")) {
              error.message = error_obj["message"].get<std::string>();
            }
            if (error_obj.contains("details")) {
              error.details = error_obj["details"].dump();
            }
          } else if (error_obj.is_string()) {
            // Simple error string
            error.message = error_obj.get<std::string>();
          }
        }
        
        // Alternative error patterns
        if (json.contains("message") && error.message.empty()) {
          error.message = json["message"].get<std::string>();
        }
        if (json.contains("code") && error.code.empty()) {
          error.code = json["code"].get<std::string>();
        }
        
      } catch (const std::exception& e) {
        // If JSON parsing fails, add that as additional context
        error.details = "Failed to parse error details: " + std::string(e.what());
      }
    }
  } else {
    // For successful HTTP responses, check for API-level errors
    if (!response.raw_json.empty()) {
      try {
        const auto& json = response.raw_json;
        
        // Check for API success indicators
        bool api_success = true;
        
        // Common patterns for API success/failure
        if (json.contains("success")) {
          api_success = json["success"].get<bool>();
        } else if (json.contains("result")) {
          const auto& result = json["result"];
          if (result.is_string()) {
            api_success = (result.get<std::string>() == "success");
          } else if (result.is_boolean()) {
            api_success = result.get<bool>();
          }
        } else if (json.contains("status")) {
          const auto& status = json["status"];
          if (status.is_string()) {
            const std::string status_str = status.get<std::string>();
            api_success = (status_str == "success" || status_str == "ok");
          }
        }
        
        // If API indicates failure, populate error information
        if (!api_success) {
          ok = false;  // Mark overall result as failed
          
          if (json.contains("error")) {
            const auto& error_obj = json["error"];
            if (error_obj.is_object()) {
              if (error_obj.contains("code")) {
                error.code = error_obj["code"].get<std::string>();
              }
              if (error_obj.contains("message")) {
                error.message = error_obj["message"].get<std::string>();
              }
            } else if (error_obj.is_string()) {
              error.message = error_obj.get<std::string>();
            }
          }
          
          if (json.contains("message") && error.message.empty()) {
            error.message = json["message"].get<std::string>();
          }
          
          if (error.message.empty()) {
            error.message = "API returned failure status";
          }
        }
        
      } catch (const std::exception& e) {
        // If we can't parse the response, that's not necessarily an error
        // Just log it in details for debugging
        error.details = "Warning: Could not parse API response: " + std::string(e.what());
      }
    }
  }
}

}  // namespace amssdk
