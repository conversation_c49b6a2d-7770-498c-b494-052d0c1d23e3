#include <iostream>
#include <string>
#include "common/api_result.h"
#include "network/response.h"

using namespace amssdk;

// Example data structure
struct UserInfo {
  std::string name;
  int age;
  std::string email;
};

// Example parser function
UserInfo parseUserInfo(const nlohmann::json& json) {
  UserInfo user;
  user.name = json.value("name", "");
  user.age = json.value("age", 0);
  user.email = json.value("email", "");
  return user;
}

void demonstrateApiResultUsage() {
  std::cout << "=== API Result Refactoring Demo ===" << std::endl;
  
  // Example 1: Successful response with data
  std::cout << "\n1. Testing successful response with data:" << std::endl;
  {
    nlohmann::json success_json = {
      {"name", "John Doe"},
      {"age", 30},
      {"email", "<EMAIL>"},
      {"result", "success"}
    };
    
    Response response("https://api.example.com/user", success_json.dump(), 
                     "200 OK", "OK", 200, 150.5);
    
    auto result = BuildResultWithParser<UserInfo>(response, parseUserInfo);
    
    std::cout << "HTTP Success: " << result.is_http_success() << std::endl;
    std::cout << "Overall Success: " << result.is_success() << std::endl;
    std::cout << "Has Data: " << result.has_data() << std::endl;
    std::cout << "Status Code: " << result.status_code << std::endl;
    std::cout << "Elapsed: " << result.elapsed_ms << "ms" << std::endl;
    
    if (result.has_data()) {
      const auto& user = result.get_data();
      std::cout << "User: " << user.name << ", Age: " << user.age 
                << ", Email: " << user.email << std::endl;
    }
  }
  
  // Example 2: HTTP error
  std::cout << "\n2. Testing HTTP error response:" << std::endl;
  {
    nlohmann::json error_json = {
      {"error", {
        {"code", "NOT_FOUND"},
        {"message", "User not found"}
      }}
    };
    
    Response response("https://api.example.com/user", error_json.dump(),
                     "404 Not Found", "Not Found", 404, 75.2);
    
    auto result = BuildResult<UserInfo>(response);
    
    std::cout << "HTTP Success: " << result.is_http_success() << std::endl;
    std::cout << "Overall Success: " << result.is_success() << std::endl;
    std::cout << "Has Error: " << result.error.has_error() << std::endl;
    std::cout << "Error Message: " << result.get_error_message() << std::endl;
    std::cout << "Error Code: " << result.error.code << std::endl;
  }
  
  // Example 3: API-level error (HTTP 200 but API failure)
  std::cout << "\n3. Testing API-level error:" << std::endl;
  {
    nlohmann::json api_error_json = {
      {"success", false},
      {"error", {
        {"code", "VALIDATION_ERROR"},
        {"message", "Invalid email format"}
      }}
    };
    
    Response response("https://api.example.com/user", api_error_json.dump(),
                     "200 OK", "OK", 200, 120.0);
    
    auto result = BuildResult<UserInfo>(response);
    
    std::cout << "HTTP Success: " << result.is_http_success() << std::endl;
    std::cout << "Overall Success: " << result.is_success() << std::endl;
    std::cout << "Error Message: " << result.get_error_message() << std::endl;
    std::cout << "Error Code: " << result.error.code << std::endl;
  }
  
  // Example 4: Void response (no data expected)
  std::cout << "\n4. Testing void response:" << std::endl;
  {
    nlohmann::json void_json = {
      {"result", "success"},
      {"message", "Operation completed"}
    };
    
    Response response("https://api.example.com/delete", void_json.dump(),
                     "200 OK", "OK", 200, 95.3);
    
    auto result = BuildResult<void>(response);
    
    std::cout << "HTTP Success: " << result.is_http_success() << std::endl;
    std::cout << "Overall Success: " << result.is_success() << std::endl;
    std::cout << "Status Code: " << result.status_code << std::endl;
    std::cout << "Raw Body: " << result.raw_body << std::endl;
  }
  
  // Example 5: Parse error handling
  std::cout << "\n5. Testing parse error handling:" << std::endl;
  {
    nlohmann::json malformed_json = {
      {"name", "Jane Doe"},
      {"age", "not_a_number"},  // This will cause parsing to fail
      {"email", "<EMAIL>"}
    };
    
    Response response("https://api.example.com/user", malformed_json.dump(),
                     "200 OK", "OK", 200, 110.0);
    
    auto result = BuildResultWithParser<UserInfo>(response, parseUserInfo);
    
    std::cout << "HTTP Success: " << result.is_http_success() << std::endl;
    std::cout << "Overall Success: " << result.is_success() << std::endl;
    std::cout << "Has Data: " << result.has_data() << std::endl;
    std::cout << "Error Message: " << result.get_error_message() << std::endl;
    std::cout << "Error Code: " << result.error.code << std::endl;
  }
  
  std::cout << "\n=== Demo completed ===" << std::endl;
}

int main() {
  try {
    demonstrateApiResultUsage();
    return 0;
  } catch (const std::exception& e) {
    std::cerr << "Error: " << e.what() << std::endl;
    return 1;
  }
}
