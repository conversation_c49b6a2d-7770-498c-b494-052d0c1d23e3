# API Result Refactoring Migration Guide

## Overview

The `ApiResult` class has been significantly refactored to address several issues and improve usability. This guide explains the changes and how to migrate existing code.

## Key Improvements

### 1. **Eliminated Redundant Fields**
- **Before**: Had both `ok` and `success` fields with unclear semantics
- **After**: Single `ok` field for HTTP success, with `is_success()` method for overall success

### 2. **Better Error Handling**
- **Before**: Limited error information, inconsistent population
- **After**: Comprehensive `SdkError` with multiple error sources and utility methods

### 3. **Type Safety**
- **Before**: Template specialization duplication
- **After**: Inheritance-based design with `std::optional<T>` for data

### 4. **Improved JSON Handling**
- **Before**: Unsafe JSON operations, brittle success detection
- **After**: Robust JSON parsing with proper error handling

### 5. **Move Semantics**
- **Before**: Unnecessary string copies
- **After**: Efficient move operations where appropriate

## API Changes

### New Structure

```cpp
// Base class with common functionality
class ApiResultBase {
public:
  bool ok = false;                    // HTTP-level success
  int status_code = 0;               // HTTP status code
  double elapsed_ms = 0.0;           // Request duration
  std::string request_url;           // Request URL
  std::string raw_body;              // Raw response body
  SdkError error;                    // Comprehensive error info
  
  // Utility methods
  bool is_http_success() const noexcept;
  bool is_success() const noexcept;
  std::string get_error_message() const;
};

// Template for typed responses
template <typename T>
struct ApiResult : public ApiResultBase {
  std::optional<T> data;             // Optional typed data
  
  bool has_data() const noexcept;
  const T& get_data() const;
  const T& get_data_or(const T& default_value) const;
  void set_data(T&& value);
};
```

### Enhanced SdkError

```cpp
struct SdkError {
  int status_code = 0;           // HTTP status code
  std::string code;              // API-specific error code
  std::string message;           // Human-readable error message
  std::string reason;            // HTTP reason phrase
  std::string details;           // Additional error details
  
  bool has_error() const noexcept;
  void clear() noexcept;
};
```

## Migration Examples

### Example 1: Basic Usage Migration

**Before:**
```cpp
ApiResult<CreateDatasetResponse> KnowledgeService::CreateDataset(
    const CreateDatasetRequest& request) const {
  const nlohmann::json json_data = SerializeCreateDatasetRequest(request);
  auto response = http_client_.Post(json_data, endpoints::kDatasets);
  auto result = BuildResult<CreateDatasetResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeCreateDatasetResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message = "Failed to deserialize CreateDatasetResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}
```

**After (Option 1 - Manual parsing):**
```cpp
ApiResult<CreateDatasetResponse> KnowledgeService::CreateDataset(
    const CreateDatasetRequest& request) const {
  const nlohmann::json json_data = SerializeCreateDatasetRequest(request);
  auto response = http_client_.Post(json_data, endpoints::kDatasets);
  auto result = BuildResult<CreateDatasetResponse>(response);

  if (result.is_success()) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.set_data(DeserializeCreateDatasetResponse(j));
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.code = "PARSE_ERROR";
      result.error.message = "Failed to deserialize CreateDatasetResponse: " +
                             std::string(e.what());
    }
  }
  return result;
}
```

**After (Option 2 - Using parser function):**
```cpp
ApiResult<CreateDatasetResponse> KnowledgeService::CreateDataset(
    const CreateDatasetRequest& request) const {
  const nlohmann::json json_data = SerializeCreateDatasetRequest(request);
  auto response = http_client_.Post(json_data, endpoints::kDatasets);
  
  return BuildResultWithParser<CreateDatasetResponse>(response, 
    [](const nlohmann::json& json) {
      return DeserializeCreateDatasetResponse(json);
    });
}
```

### Example 2: Error Checking Migration

**Before:**
```cpp
auto result = client.AppInfo(request);
if (result.ok) {
  std::cout << "Success: " << result.raw_body << std::endl;
} else {
  std::cerr << "Error: " << result.error.message << std::endl;
}
```

**After:**
```cpp
auto result = client.AppInfo(request);
if (result.is_success()) {
  if (result.has_data()) {
    const auto& app_info = result.get_data();
    // Use typed data
  }
  std::cout << "Success: " << result.raw_body << std::endl;
} else {
  std::cerr << "Error: " << result.get_error_message() << std::endl;
  if (!result.error.code.empty()) {
    std::cerr << "Error Code: " << result.error.code << std::endl;
  }
}
```

### Example 3: Void Response Migration

**Before:**
```cpp
ApiResult<void> result = BuildResult<void>(response);
if (result.ok) {
  // Success
}
```

**After:**
```cpp
ApiResult<void> result = BuildResult<void>(response);
if (result.is_success()) {
  // Success - same usage
}
```

## Breaking Changes

1. **Removed `success` field**: Use `is_success()` method instead
2. **Data field is now optional**: Use `has_data()` and `get_data()` methods
3. **Enhanced error structure**: `SdkError` has additional fields
4. **New utility methods**: Prefer `is_success()` over direct `ok` access

## Backward Compatibility Notes

- The `ok` field still exists and has the same meaning (HTTP-level success)
- `BuildResult<T>()` function signature is unchanged
- Basic error checking patterns still work but should be updated to use new methods

## Recommended Migration Steps

1. **Update error checking**: Replace `result.ok` with `result.is_success()` where appropriate
2. **Update data access**: Replace direct `result.data` access with `result.get_data()` or `result.has_data()`
3. **Enhance error handling**: Use `result.get_error_message()` and check `result.error.code`
4. **Consider using parser functions**: For cleaner code, use `BuildResultWithParser()`
5. **Update includes**: Ensure `#include <nlohmann/json.hpp>` is present where needed

## Testing

The refactored code includes comprehensive error handling and should be more robust. Test the following scenarios:

1. Successful HTTP responses with valid data
2. HTTP error responses (4xx, 5xx)
3. Successful HTTP responses with API-level errors
4. Malformed JSON responses
5. Network timeouts and connection errors

## Benefits

- **Clearer semantics**: Distinction between HTTP and API-level success
- **Better error information**: More detailed error context
- **Type safety**: Optional data prevents accessing uninitialized values
- **Performance**: Move semantics reduce unnecessary copies
- **Maintainability**: Inheritance reduces code duplication
